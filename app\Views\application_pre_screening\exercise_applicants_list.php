<?= $this->extend('layouts/main') ?>

<?= $this->section('title') ?>
<?= esc($title) ?>
<?= $this->endSection() ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                            <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Exercise Applicants</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="<?= base_url('application_pre_screening') ?>" class="btn btn-secondary">
                        <i class="fas fa-arrow-left"></i> Back to Exercises
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Exercise Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">Exercise Information</h5>
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Exercise Name:</strong> <?= esc($exercise['exercise_name']) ?></p>
                            <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Status:</strong> 
                                <span class="badge badge-<?= $exercise['status'] === 'published' ? 'success' : 'warning' ?>">
                                    <?= ucfirst(esc($exercise['status'])) ?>
                                </span>
                            </p>
                            <p><strong>Total Unique Applicants:</strong> <?= count($applicants) ?></p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Applicants List -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-users"></i> Unique Applicants List
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (empty($applicants)): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-users fa-3x text-muted mb-3"></i>
                            <h5 class="text-muted">No Applicants Found</h5>
                            <p class="text-muted">No applicants have applied for positions in this exercise yet.</p>
                        </div>
                    <?php else: ?>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead class="thead-dark">
                                    <tr>
                                        <th scope="col">#</th>
                                        <th scope="col">Applicant ID</th>
                                        <th scope="col">Full Name</th>
                                        <th scope="col">Email Address</th>
                                        <th scope="col">Gender</th>
                                        <th scope="col">Contact Details</th>
                                        <th scope="col">Applications Count</th>
                                        <th scope="col">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($applicants as $index => $applicant): ?>
                                        <tr>
                                            <td><?= $index + 1 ?></td>
                                            <td>
                                                <span class="badge badge-primary"><?= esc($applicant['applicant_id']) ?></span>
                                            </td>
                                            <td>
                                                <strong><?= esc($applicant['full_name']) ?></strong>
                                            </td>
                                            <td>
                                                <a href="mailto:<?= esc($applicant['email_address']) ?>" class="text-decoration-none">
                                                    <?= esc($applicant['email_address']) ?>
                                                </a>
                                            </td>
                                            <td>
                                                <span class="badge badge-<?= $applicant['gender'] === 'Male' ? 'info' : 'pink' ?>">
                                                    <?= esc($applicant['gender']) ?>
                                                </span>
                                            </td>
                                            <td><?= esc($applicant['contact_details']) ?></td>
                                            <td>
                                                <span class="badge badge-success"><?= esc($applicant['application_count']) ?></span>
                                            </td>
                                            <td>
                                                <div class="btn-group" role="group">
                                                    <button type="button" class="btn btn-sm btn-outline-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                                                        Actions
                                                    </button>
                                                    <div class="dropdown-menu">
                                                        <a class="dropdown-item" href="<?= base_url('applicant_profile/view/' . $applicant['applicant_id']) ?>">
                                                            <i class="fas fa-user"></i> View Profile
                                                        </a>
                                                        <a class="dropdown-item" href="<?= base_url('application_pre_screening/applicant_applications/' . $applicant['applicant_id'] . '/' . $exercise['id']) ?>">
                                                            <i class="fas fa-file-alt"></i> View Applications
                                                        </a>
                                                    </div>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>

                        <!-- Summary Information -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="alert alert-info">
                                    <i class="fas fa-info-circle"></i>
                                    <strong>Summary:</strong> 
                                    This list shows <?= count($applicants) ?> unique applicants who have applied for positions in this exercise. 
                                    Each applicant is listed only once, regardless of how many positions they applied for.
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.badge-pink {
    color: #fff;
    background-color: #e91e63;
}
</style>
<?= $this->endSection() ?>
