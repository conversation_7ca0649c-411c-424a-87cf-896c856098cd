<?php

namespace App\Controllers;

class ApplicationPreScreeningController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = \Config\Services::session();
    }

    /**
     * [GET] Display exercises in selection status for pre-screening context.
     * URI: /application_pre_screening/exercises
     */
    public function exercises()
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load ExerciseModel and get exercises where status != 'draft'
        $exerciseModel = new \App\Models\ExerciseModel();
        $exercises = $exerciseModel->getPreScreeningExercises($orgId);

        $data = [
            'title' => 'Exercises Available for Pre-Screening',
            'menu' => 'applications',
            'exercises' => $exercises
        ];

        return view('application_pre_screening/application_pre_screening_exercise_list', $data);
    }

    /**
     * [GET] Display unique applicants within an exercise for pre-screening.
     * URI: /application_pre_screening/exercise/{exerciseId}/prescreening_applicants
     */
    public function exercisePreScreeningApplicants($exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get unique applicants for this exercise
        $applicants = $applicationModel->select('
                appx_application_details.applicant_id,
                appx_application_details.first_name,
                appx_application_details.last_name,
                appx_application_details.email_address,
                appx_application_details.gender,
                appx_application_details.contact_details,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name,
                COUNT(appx_application_details.id) as application_count
            ')
            ->where('exercise_id', $exerciseId)
            ->where('org_id', $orgId)
            ->groupBy('applicant_id')
            ->orderBy('full_name', 'ASC')
            ->findAll();

        $data = [
            'title' => 'Applicants in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicants' => $applicants
        ];

        return view('application_pre_screening/exercise_prescreening_applicants_list', $data);
    }

    /**
     * [GET] Display the detailed view of a specific application for pre-screening.
     * URI: /application_pre_screening/show/{id}
     *
     * @param int $id Application ID
     * @return string|\CodeIgniter\HTTP\RedirectResponse
     */
    public function show($id)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get application with related data
        $application = $applicationModel->getApplicationWithDetails($id, $orgId);

        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Extract exercise data from application
        $exercise = [
            'id' => $application['exercise_id'],
            'exercise_name' => $application['exercise_name'],
            'gazzetted_no' => $application['gazzetted_no'],
            'advertisement_no' => $application['advertisement_no'],
            'gazzetted_date' => $application['gazzetted_date'],
            'advertisement_date' => $application['advertisement_date'],
            'is_internal' => $application['is_internal'],
            'mode_of_advertisement' => $application['mode_of_advertisement'],
            'publish_date_from' => $application['publish_date_from'],
            'publish_date_to' => $application['publish_date_to']
        ];

        // Extract position data from application
        $position = [
            'id' => $application['position_id'],
            'position_reference' => $application['position_reference'],
            'designation' => $application['position_title'],
            'classification' => $application['classification'],
            'location' => $application['position_location'],
            'annual_salary' => $application['annual_salary'],
            'qualifications' => $application['qualifications'],
            'knowledge' => $application['knowledge'],
            'skills_competencies' => $application['skills_competencies'],
            'job_experiences' => $application['job_experiences']
        ];

        // Extract pre-screening criteria from exercise if available
        $preScreenCriteria = [];
        if (!empty($application['pre_screen_criteria'])) {
            $preScreenCriteria = json_decode($application['pre_screen_criteria'], true) ?? [];
        }

        $data = [
            'title' => 'Application Pre-Screening',
            'menu' => 'applications',
            'application' => $application,
            'exercise' => $exercise,
            'position' => $position,
            'preScreenCriteria' => $preScreenCriteria
        ];

        return view('application_pre_screening/application_pre_screening_detailed_view', $data);
    }

    /**
     * [POST] Save pre-screening results for an application.
     * URI: /application_pre_screening/save/{id}
     *
     * @param int $id Application ID
     * @return \CodeIgniter\HTTP\RedirectResponse
     */
    public function save($id)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Find the application first to ensure it exists
        $application = $applicationModel->getApplicationWithDetails($id, $orgId);
        if (!$application) {
            return redirect()->to(base_url('application_pre_screening'))
                ->with('error', 'Application not found.');
        }

        // Process criteria results
        $criteriaIndices = $this->request->getPost('criteria_indices') ?? [];
        $criteriaMet = $this->request->getPost('criteria_met') ?? [];
        $criteriaRemarks = $this->request->getPost('criteria_remarks') ?? [];

        $criteriaResults = [];
        foreach ($criteriaIndices as $criteriaIndex) {
            $criteriaResults[] = [
                'criteriaIndex' => $criteriaIndex,
                'met' => isset($criteriaMet[$criteriaIndex]) ? true : false,
                'remarks' => trim($criteriaRemarks[$criteriaIndex] ?? '')
            ];
        }

        // Prepare data for update
        $data = [
            'pre_screened_at' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $this->request->getPost('status'),
            'pre_screened_remarks' => trim($this->request->getPost('remarks')),
            'pre_screened_criteria_results' => json_encode($criteriaResults),
            'updated_by' => $this->session->get('user_id')
        ];

        // Update the application
        if ($applicationModel->update($id, $data)) {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('success', 'Pre-screening results saved successfully.');
        } else {
            return redirect()->to(base_url('application_pre_screening/show/' . $id))
                ->with('error', 'Failed to save pre-screening results.');
        }
    }

    /**
     * [POST] Batch pre-screen multiple applications.
     * URI: /application_pre_screening/batch_update
     */
    public function batchUpdate()
    {
        $ids = $this->request->getPost('ids');
        $status = $this->request->getPost('status');
        $remarks = trim($this->request->getPost('remarks') ?? '');

        if (empty($ids) || !is_array($ids)) {
            $this->session->setFlashdata('error', 'No applications selected.');
            return redirect()->back();
        }

        if (empty($status)) {
            $this->session->setFlashdata('error', 'Status is required.');
            return redirect()->back()->withInput();
        }

        if ($status === 'failed' && empty($remarks)) {
             $this->session->setFlashdata('error', 'Remarks are required when batch failing applications.');
            return redirect()->back()->withInput();
        }

        // Load model
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Prepare batch update data
        $data = [
            'pre_screened_at' => date('Y-m-d H:i:s'),
            'pre_screened_by' => $this->session->get('user_id'),
            'pre_screened_status' => $status,
            'pre_screened_remarks' => "[Batch Update] " . $remarks,
            'pre_screened_criteria_results' => null,
            'updated_at' => date('Y-m-d H:i:s'),
            'updated_by' => $this->session->get('user_id')
        ];

        $successCount = 0;
        $failCount = 0;

        foreach ($ids as $id) {
            $id = (int) $id;
            if ($id > 0) {
                try {
                    if ($applicationModel->update($id, $data)) {
                        $successCount++;
                    } else {
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    log_message('error', 'Exception during batch pre-screening for ID ' . $id . ': ' . $e->getMessage());
                    $failCount++;
                }
            } else {
                $failCount++;
            }
        }

        // Set Flash Message
        $message = '';
        if ($successCount > 0) {
            $message .= "$successCount application(s) pre-screened successfully. ";
        }
        if ($failCount > 0) {
            $message .= "$failCount application(s) failed to update.";
        }

        if ($successCount > 0) {
            $this->session->setFlashdata('success', $message);
        } else {
            $this->session->setFlashdata('error', $message);
        }

        return redirect()->back();
    }

    /**
     * [GET] Display all applications for a specific applicant within an exercise.
     * URI: /application_pre_screening/applicant_applications/{applicantId}/{exerciseId}
     */
    public function applicantApplications($applicantId, $exerciseId)
    {
        $orgId = $this->session->get('org_id');
        if (!$orgId) {
            $this->session->setFlashdata('error', 'Organization context not found.');
            return redirect()->to(base_url());
        }

        // Load models
        $exerciseModel = new \App\Models\ExerciseModel();
        $applicationModel = new \App\Models\AppxApplicationDetailsModel();

        // Get exercise details
        $exercise = $exerciseModel->find($exerciseId);
        if (!$exercise || $exercise['org_id'] != $orgId) {
            $this->session->setFlashdata('error', 'Exercise not found or access denied.');
            return redirect()->to(base_url('application_pre_screening'));
        }

        // Get all applications for this applicant in this exercise
        $applications = $applicationModel->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions_groups.group_name,
                CONCAT(appx_application_details.first_name, " ", appx_application_details.last_name) as full_name
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->where('appx_application_details.applicant_id', $applicantId)
            ->where('appx_application_details.exercise_id', $exerciseId)
            ->where('appx_application_details.org_id', $orgId)
            ->orderBy('appx_application_details.created_at', 'DESC')
            ->findAll();

        if (empty($applications)) {
            $this->session->setFlashdata('error', 'No applications found for this applicant in this exercise.');
            return redirect()->to(base_url('application_pre_screening/exercise/' . $exerciseId . '/prescreening_applicants'));
        }

        // Get applicant name from first application
        $applicantName = $applications[0]['full_name'];

        $data = [
            'title' => 'Applications by ' . esc($applicantName) . ' in Exercise: ' . esc($exercise['exercise_name']),
            'menu' => 'applications',
            'exercise' => $exercise,
            'applicant_name' => $applicantName,
            'applicant_id' => $applicantId,
            'applications' => $applications
        ];

        return view('application_pre_screening/applicant_applications_list', $data);
    }
}
