<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<!-- <PERSON> Header -->
<div class="row mb-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center">
            <div>
                <h1 class="h3 mb-0"><?= esc($title) ?></h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="<?= base_url('dashboard') ?>">Dashboard</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening') ?>">Pre-Screening</a></li>
                        <li class="breadcrumb-item"><a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>">Pre-Screening Applicants</a></li>
                        <li class="breadcrumb-item active" aria-current="page">Pre-Screening Profile</li>
                    </ol>
                </nav>
            </div>
            <div>
                <a href="<?= base_url('application_pre_screening/exercise/' . $exercise['id'] . '/prescreening_applicants') ?>" class="btn btn-secondary">
                    <i class="fas fa-arrow-left"></i> Back to Applicants
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Exercise & Applicant Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-red text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-user-check"></i> Pre-Screening Profile Overview
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6 class="text-red">Applicant Information</h6>
                        <p><strong>Name:</strong> <?= esc($applicant_name) ?></p>
                        <p><strong>Applicant ID:</strong> <span class="badge bg-primary"><?= esc($applicant_id) ?></span></p>
                        <p><strong>Email:</strong> <?= esc($applicant['email']) ?></p>
                        <p><strong>Status:</strong> 
                            <span class="badge bg-<?= $applicant['status'] === 'active' ? 'success' : 'warning' ?>">
                                <?= ucfirst(esc($applicant['status'])) ?>
                            </span>
                        </p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-red">Exercise Information</h6>
                        <p><strong>Exercise:</strong> <?= esc($exercise['exercise_name']) ?></p>
                        <p><strong>Advertisement No:</strong> <?= esc($exercise['advertisement_no']) ?></p>
                        <p><strong>Total Applications:</strong> <span class="badge bg-success"><?= count($applications) ?></span></p>
                        <p><strong>Profile Created:</strong> <?= date('M d, Y', strtotime($applicant['created_at'])) ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Applications Summary for Pre-Screening -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-yellow text-black">
                <h5 class="card-title mb-0">
                    <i class="fas fa-clipboard-list"></i> Applications Summary for Pre-Screening
                </h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-striped table-hover" id="applicationsTable">
                        <thead class="table-dark">
                            <tr>
                                <th scope="col">#</th>
                                <th scope="col">Application No.</th>
                                <th scope="col">Position</th>
                                <th scope="col">Position Group</th>
                                <th scope="col">Classification</th>
                                <th scope="col">Pre-Screen Status</th>
                                <th scope="col">Applied Date</th>
                                <th scope="col">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($applications as $index => $application): ?>
                                <tr>
                                    <td><?= $index + 1 ?></td>
                                    <td>
                                        <span class="badge bg-info"><?= esc($application['application_number']) ?></span>
                                    </td>
                                    <td>
                                        <strong class="text-red"><?= esc($application['position_title']) ?></strong><br>
                                        <small class="text-muted"><?= esc($application['position_reference']) ?></small>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary"><?= esc($application['group_name']) ?></span>
                                    </td>
                                    <td><?= esc($application['classification']) ?></td>
                                    <td>
                                        <?php
                                        $preScreenStatus = $application['pre_screened_status'] ?? 'Not Screened';
                                        $preScreenClass = 'secondary';
                                        switch (strtolower($preScreenStatus)) {
                                            case 'passed':
                                                $preScreenClass = 'success';
                                                break;
                                            case 'failed':
                                                $preScreenClass = 'danger';
                                                break;
                                            case 'not screened':
                                                $preScreenClass = 'warning';
                                                break;
                                        }
                                        ?>
                                        <span class="badge bg-<?= $preScreenClass ?>"><?= esc($preScreenStatus) ?></span>
                                    </td>
                                    <td>
                                        <?= date('M d, Y', strtotime($application['created_at'])) ?><br>
                                        <small class="text-muted"><?= date('H:i', strtotime($application['created_at'])) ?></small>
                                    </td>
                                    <td>
                                        <div class="d-flex gap-1">
                                            <a href="<?= base_url('application_pre_screening/show/' . $application['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary" title="Pre-Screen Application">
                                                <i class="fas fa-clipboard-check"></i>
                                            </a>
                                            <a href="<?= base_url('acknowledged_applications/view/' . $application['id']) ?>" 
                                               class="btn btn-sm btn-outline-info" title="View Full Details">
                                                <i class="fas fa-eye"></i>
                                            </a>
                                        </div>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pre-Screening Quick Actions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card hover-card">
            <div class="card-header bg-black text-white">
                <h5 class="card-title mb-0">
                    <i class="fas fa-tools"></i> Pre-Screening Quick Actions
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="d-grid">
                            <a href="<?= base_url('application_pre_screening/applicant_applications/' . $applicant_id . '/' . $exercise['id']) ?>" 
                               class="btn btn-outline-success btn-lg">
                                <i class="fas fa-file-alt"></i><br>
                                <span>View All Applications</span>
                            </a>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-warning btn-lg" onclick="batchPreScreen('passed')">
                                <i class="fas fa-check-circle"></i><br>
                                <span>Pass All Applications</span>
                            </button>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="d-grid">
                            <button type="button" class="btn btn-outline-danger btn-lg" onclick="batchPreScreen('failed')">
                                <i class="fas fa-times-circle"></i><br>
                                <span>Fail All Applications</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pre-Screening Statistics -->
<div class="row">
    <div class="col-md-3">
        <div class="card hover-card text-center">
            <div class="card-body">
                <h3 class="text-primary"><?= count($applications) ?></h3>
                <p class="text-muted mb-0">Total Applications</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card hover-card text-center">
            <div class="card-body">
                <h3 class="text-success">
                    <?= count(array_filter($applications, function($app) { return strtolower($app['pre_screened_status'] ?? '') === 'passed'; })) ?>
                </h3>
                <p class="text-muted mb-0">Passed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card hover-card text-center">
            <div class="card-body">
                <h3 class="text-danger">
                    <?= count(array_filter($applications, function($app) { return strtolower($app['pre_screened_status'] ?? '') === 'failed'; })) ?>
                </h3>
                <p class="text-muted mb-0">Failed</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card hover-card text-center">
            <div class="card-body">
                <h3 class="text-warning">
                    <?= count(array_filter($applications, function($app) { return empty($app['pre_screened_status']) || strtolower($app['pre_screened_status']) === 'not screened'; })) ?>
                </h3>
                <p class="text-muted mb-0">Pending</p>
            </div>
        </div>
    </div>
</div>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#applicationsTable').DataTable({
        "pageLength": 10,
        "order": [[ 6, "desc" ]], // Sort by Applied Date descending
        "columnDefs": [
            { "orderable": false, "targets": [7] } // Disable sorting on Actions column
        ],
        "language": {
            "search": "Search Applications:",
            "lengthMenu": "Show _MENU_ applications per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ applications",
            "infoEmpty": "No applications found",
            "infoFiltered": "(filtered from _MAX_ total applications)"
        }
    });
});

function batchPreScreen(status) {
    const applicationIds = <?= json_encode(array_column($applications, 'id')) ?>;
    
    Swal.fire({
        title: 'Confirm Batch Pre-Screening',
        text: `Are you sure you want to ${status} all applications for this applicant?`,
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: status === 'passed' ? '#28a745' : '#dc3545',
        cancelButtonColor: '#6c757d',
        confirmButtonText: `Yes, ${status} all!`,
        cancelButtonText: 'Cancel'
    }).then((result) => {
        if (result.isConfirmed) {
            // Create form and submit
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = '<?= base_url('application_pre_screening/batch_update') ?>';
            
            // Add CSRF token
            const csrfInput = document.createElement('input');
            csrfInput.type = 'hidden';
            csrfInput.name = '<?= csrf_token() ?>';
            csrfInput.value = '<?= csrf_hash() ?>';
            form.appendChild(csrfInput);
            
            // Add application IDs
            applicationIds.forEach(id => {
                const input = document.createElement('input');
                input.type = 'hidden';
                input.name = 'ids[]';
                input.value = id;
                form.appendChild(input);
            });
            
            // Add status
            const statusInput = document.createElement('input');
            statusInput.type = 'hidden';
            statusInput.name = 'status';
            statusInput.value = status;
            form.appendChild(statusInput);
            
            // Add remarks if failed
            if (status === 'failed') {
                const remarksInput = document.createElement('input');
                remarksInput.type = 'hidden';
                remarksInput.name = 'remarks';
                remarksInput.value = 'Batch pre-screening: Failed';
                form.appendChild(remarksInput);
            }
            
            document.body.appendChild(form);
            form.submit();
        }
    });
}
</script>
<?= $this->endSection() ?>
