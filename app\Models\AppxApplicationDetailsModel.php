<?php

namespace App\Models;

use CodeIgniter\Model;

/**
 * AppxApplicationDetailsModel
 *
 * Model for the appx_application_details table
 */
class AppxApplicationDetailsModel extends Model
{
    protected $table         = 'appx_application_details';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField  = 'deleted_at';
    protected $protectFields = true;

    // Fields that can be set during save, insert, update
    protected $allowedFields = [
        'org_id', 'exercise_id', 'applicant_id', 'position_id', 'application_number', 'email_address',
        'first_name', 'last_name', 'gender', 'date_of_birth', 'place_of_origin',
        'id_photo_path', 'contact_details', 'location_address', 'id_numbers',
        'current_employer', 'current_position', 'current_salary', 'citizenship',
        'marital_status', 'date_of_marriage', 'spouse_employer', 'is_public_servant',
        'public_service_file_number', 'employee_of_org_id', 'children',
        'offence_convicted', 'referees', 'how_did_you_hear_about_us', 'signature_path',
        'publications', 'awards', 'is_received', 'received_status', 'received_by',
        'received_at', 'application_status', 'remarks', 'created_by', 'updated_by', 'deleted_by',
        'pre_screened_at', 'pre_screened_by', 'pre_screened_status', 'pre_screened_remarks',
        'pre_screened_criteria_results', 'profile_status', 'profile_details',
        'profiled_by', 'profiled_at', 'rating_capability_max', 'rating_remarks',
        'rating_status', 'rated_by', 'rated_at', 'shortlist_status', 'shortlisted_by',
        'shortlisted_at', 'interviewed', 'interviewed_by', 'interviewed_at', 'interview_rated',
        'pre_selection_status', 'pre_selection_by', 'preselection_at', 'selected_status',
        'selected_by', 'selected_at'
    ];

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'org_id'             => 'required|numeric',
        'exercise_id'        => 'required|numeric',
        'applicant_id'       => 'required|numeric',
        'position_id'        => 'required|numeric',
        'application_number' => 'required|max_length[20]',
        'email_address'      => 'required|valid_email|max_length[255]',
        'first_name'         => 'required|max_length[100]',
        'last_name'          => 'required|max_length[100]',
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_id' => [
            'required' => 'Exercise ID is required',
            'numeric'  => 'Exercise ID must be a number'
        ],
        'applicant_id' => [
            'required' => 'Applicant ID is required',
            'numeric'  => 'Applicant ID must be a number'
        ],
        'position_id' => [
            'required' => 'Position ID is required',
            'numeric'  => 'Position ID must be a number'
        ],
        'application_number' => [
            'required'    => 'Application number is required',
            'max_length'  => 'Application number cannot exceed 20 characters'
        ],
        'email_address' => [
            'required'    => 'Email address is required',
            'valid_email' => 'Please provide a valid email address',
            'max_length'  => 'Email address cannot exceed 255 characters'
        ],
        'first_name' => [
            'required'    => 'First name is required',
            'max_length'  => 'First name cannot exceed 100 characters'
        ],
        'last_name' => [
            'required'    => 'Last name is required',
            'max_length'  => 'Last name cannot exceed 100 characters'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get application by ID
     *
     * @param int $id
     * @return array|null
     */
    public function getApplicationById($id)
    {
        return $this->find($id);
    }

    /**
     * Get applications by applicant ID
     *
     * @param int $applicantId
     * @return array
     */
    public function getApplicationsByApplicantId($applicantId)
    {
        return $this->where('applicant_id', $applicantId)->findAll();
    }

    /**
     * Get applications by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getApplicationsByPositionId($positionId)
    {
        return $this->where('position_id', $positionId)->findAll();
    }

    /**
     * Get application by application number
     *
     * @param string $applicationNumber
     * @return array|null
     */
    public function getApplicationByNumber($applicationNumber)
    {
        return $this->where('application_number', $applicationNumber)->first();
    }

    /**
     * Get applications by pre-screening status
     *
     * @param string $status
     * @return array
     */
    public function getApplicationsByPreScreeningStatus($status)
    {
        return $this->where('pre_screened_status', $status)->findAll();
    }

    /**
     * Get applications pending pre-screening (received but not pre-screened)
     *
     * @return array
     */
    public function getApplicationsPendingPreScreening()
    {
        return $this->where('is_received', 1)
                    ->where('(pre_screened_at IS NULL OR pre_screened_status IS NULL)')
                    ->findAll();
    }

    /**
     * Get applications by profile status
     *
     * @param string $status
     * @return array
     */
    public function getApplicationsByProfileStatus($status)
    {
        return $this->where('profile_status', $status)->findAll();
    }

    /**
     * Get applications that have been profiled
     *
     * @return array
     */
    public function getProfiledApplications()
    {
        return $this->where('profiled_at IS NOT NULL')
                    ->where('profile_status IS NOT NULL')
                    ->findAll();
    }

    /**
     * Get applications that have been rated
     *
     * @return array
     */
    public function getRatedApplications()
    {
        return $this->where('rated_at IS NOT NULL')
                    ->where('rating_status IS NOT NULL')
                    ->findAll();
    }

    /**
     * Get applications by exercise ID
     *
     * @param int $exerciseId
     * @return array
     */
    public function getApplicationsByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->orderBy('created_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get applications by exercise ID where is_received is true
     *
     * @param int $exerciseId
     * @return array
     */
    public function getReceivedApplicationsByExerciseId($exerciseId)
    {
        return $this->where('exercise_id', $exerciseId)
                    ->where('is_received', 1)
                    ->orderBy('received_at', 'DESC')
                    ->findAll();
    }

    /**
     * Get unacknowledged applications with related data
     *
     * @param int|null $orgId Optional organization ID to filter by
     * @return array
     */
    public function getUnacknowledgedApplicationsWithDetails($orgId = null)
    {
        $builder = $this->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                dakoii_org.org_name,
                dakoii_org.org_code,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->where('(appx_application_details.received_status IS NULL OR appx_application_details.received_status = "")')
            ->where('appx_application_details.is_received', 0);

        // Filter by organization if provided
        if ($orgId !== null) {
            $builder->where('appx_application_details.org_id', $orgId);
        }

        return $builder->orderBy('appx_application_details.created_at', 'ASC')
            ->findAll();
    }

    /**
     * Get acknowledged applications with related data
     *
     * @param int|null $orgId Optional organization ID to filter by
     * @return array
     */
    public function getAcknowledgedApplicationsWithDetails($orgId = null)
    {
        $builder = $this->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                dakoii_org.org_name,
                dakoii_org.org_code,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->where('appx_application_details.is_received', 1);

        // Filter by organization if provided
        if ($orgId !== null) {
            $builder->where('appx_application_details.org_id', $orgId);
        }

        return $builder->orderBy('appx_application_details.received_at', 'DESC')
            ->findAll();
    }

    /**
     * Get application with full details by ID
     *
     * @param int $id
     * @param int|null $orgId Optional organization ID to filter by for security
     * @return array|null
     */
    public function getApplicationWithDetails($id, $orgId = null)
    {
        $builder = $this->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions.annual_salary,
                positions.qualifications,
                positions.knowledge,
                positions.skills_competencies,
                positions.job_experiences,
                dakoii_org.org_name,
                dakoii_org.org_code,
                exercises.exercise_name,
                exercises.gazzetted_no,
                exercises.gazzetted_date,
                exercises.advertisement_no,
                exercises.advertisement_date,
                exercises.is_internal,
                exercises.mode_of_advertisement,
                exercises.publish_date_from,
                exercises.publish_date_to
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->where('appx_application_details.id', $id);

        // Filter by organization if provided for security
        if ($orgId !== null) {
            $builder->where('appx_application_details.org_id', $orgId);
        }

        return $builder->first();
    }

    /**
     * Count applications by position ID
     *
     * @param int $positionId
     * @return int
     */
    public function countApplicationsByPositionId($positionId)
    {
        return $this->where('position_id', $positionId)->countAllResults();
    }

    /**
     * Get application statistics by position ID
     *
     * @param int $positionId
     * @return array
     */
    public function getApplicationStatsByPositionId($positionId)
    {
        $stats = [];

        // Total applications
        $stats['total'] = $this->where('position_id', $positionId)->countAllResults();

        // Applications by pre-screening status
        $stats['pre_screened'] = $this->where('position_id', $positionId)
                                      ->where('pre_screened_status IS NOT NULL')
                                      ->countAllResults();

        $stats['pending_pre_screening'] = $this->where('position_id', $positionId)
                                               ->where('is_received', 1)
                                               ->where('(pre_screened_at IS NULL OR pre_screened_status IS NULL)')
                                               ->countAllResults();

        return $stats;
    }

    /**
     * Get received applications by position ID with position and exercise details
     *
     * @param int $positionId
     * @param int|null $orgId Optional organization ID to filter by
     * @return array
     */
    public function getReceivedApplicationsByPositionId($positionId, $orgId = null)
    {
        $builder = $this->select('
                appx_application_details.*,
                positions.position_reference,
                positions.designation as position_title,
                positions.classification,
                positions.location as position_location,
                positions.annual_salary,
                positions_groups.group_name,
                dakoii_org.org_name,
                dakoii_org.org_code,
                exercises.exercise_name,
                exercises.advertisement_no
            ')
            ->join('positions', 'appx_application_details.position_id = positions.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('dakoii_org', 'appx_application_details.org_id = dakoii_org.id', 'left')
            ->join('exercises', 'appx_application_details.exercise_id = exercises.id', 'left')
            ->where('appx_application_details.position_id', $positionId)
            ->where('appx_application_details.is_received', 1)
            ->orderBy('appx_application_details.created_at', 'DESC');

        // Filter by organization if provided for security
        if ($orgId !== null) {
            $builder->where('appx_application_details.org_id', $orgId);
        }

        return $builder->findAll();
    }
}
